using Application.Abstraction.Mediator;
using Application.Features.Generic.Commands;
using Application.Features.Generic.Queries;
using Application.Features.Sample;
using Domain.Entities;
using Microsoft.AspNetCore.Mvc;

namespace API.Endpoints;

public static class SampleEntitiesEndpoints
{
    public static IEndpointRouteBuilder MapSampleEntityEndpoints(this IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/v1/sample-entities")
            .WithTags("SampleEntities");
            //.RequireAuthorization();

        group.MapGet("/", async ([FromServices] IMediator mediator) =>
        {
            var items = await mediator.Send(new GetAllEntitiesQuery<SampleDto>());
            if (!items.Succeeded)
                return Results.BadRequest(items.Errors);
            return Results.Ok(items.Data);  
        })
        .WithName("GetAllEntities")
        .WithSummary("Získá seznam všech vzorových entit")
        .WithDescription("Vrací seznam všech vzorových entit v systému")
        .Produces<List<SampleDto>>(StatusCodes.Status200OK)
        .Produces(StatusCodes.Status400BadRequest)
        .Produces(StatusCodes.Status401Unauthorized)
        .Produces(StatusCodes.Status500InternalServerError);
        
        group.MapGet("/{id:int}", async (int id, [FromServices] IMediator mediator) =>
        {
            if (id <= 0)
                return Results.BadRequest("ID musí být kladné číslo");

            var entity = await mediator.Send(new GetEntityByIdCachedQuery1<SampleDto> { Id = id });
            return entity is not null ? Results.Ok(entity) : Results.NotFound();
        })
        .WithName("GetEntityById")
        .WithSummary("Získá vzorovou entitu podle ID")
        .WithDescription("Vrací detail vzorové entity na základě jejího ID")
        .Produces<SampleDto>(StatusCodes.Status200OK)
        .Produces(StatusCodes.Status400BadRequest)
        .Produces(StatusCodes.Status404NotFound)
        .Produces(StatusCodes.Status401Unauthorized)
        .Produces(StatusCodes.Status500InternalServerError);

        group.MapPost("/", async ([FromBody] SampleAddEdit input, [FromServices] IMediator mediator) =>
        {
            var result = await mediator.Send(new CreateEntityCommand<SampleEntity, SampleAddEdit, int> { Payload = input });
            if (!result.Succeeded)
                return Results.BadRequest(result.Errors);

            // Získání vytvořené entity pro vrácení v response
            var createdEntity = await mediator.Send(new GetEntityByIdQuery<SampleDto> { Id = result.Data });
            return Results.Created($"/v1/sample-entities/{result.Data}", createdEntity);
        })
        .WithName("CreateEntity")
        .WithSummary("Vytvoří novou vzorovou entitu")
        .WithDescription("Vytvoří novou vzorovou entitu na základě poskytnutých dat")
        .Accepts<SampleAddEdit>("application/json")
        .Produces<SampleDto>(StatusCodes.Status201Created)
        .Produces(StatusCodes.Status400BadRequest)
        .Produces(StatusCodes.Status401Unauthorized)
        .Produces(StatusCodes.Status409Conflict)
        .Produces(StatusCodes.Status500InternalServerError)
        .ProducesValidationProblem();
        
        group.MapPut("/{id:int}", async (int id, [FromBody] SampleAddEdit input, [FromServices] IMediator mediator) =>
        {
            if (id <= 0)
                return Results.BadRequest("ID musí být kladné číslo");

            var result = await mediator.Send(new UpdateEntityCommand<SampleEntity, SampleAddEdit, int> { Id = id, Payload = input });
            if (!result.Succeeded)
                return Results.BadRequest(result.Errors);
            return result.Data ? Results.NoContent() : Results.NotFound();
        })
        .WithName("UpdateEntity")
        .WithSummary("Aktualizuje vzorovou entitu")
        .WithDescription("Aktualizuje existující vzorovou entitu na základě poskytnutých dat")
        .Accepts<SampleAddEdit>("application/json")
        .Produces(StatusCodes.Status204NoContent)
        .Produces(StatusCodes.Status400BadRequest)
        .Produces(StatusCodes.Status404NotFound)
        .Produces(StatusCodes.Status401Unauthorized)
        .Produces(StatusCodes.Status500InternalServerError)
        .ProducesValidationProblem();

        group.MapDelete("/{id:int}", async (int id, [FromServices] IMediator mediator) =>
        {
            if (id <= 0)
                return Results.BadRequest("ID musí být kladné číslo");

            var result = await mediator.Send(new DeleteEntityCommand<SampleEntity, int> { Id = id });
            if (!result.Succeeded)
                return Results.BadRequest(result.Errors);
            return result.Data ? Results.NoContent() : Results.NotFound();
        })
        .WithName("DeleteEntity")
        .WithSummary("Smaže vzorovou entitu")
        .WithDescription("Smaže existující vzorovou entitu na základě jejího ID")
        .Produces(StatusCodes.Status204NoContent)
        .Produces(StatusCodes.Status400BadRequest)
        .Produces(StatusCodes.Status404NotFound)
        .Produces(StatusCodes.Status401Unauthorized)
        .Produces(StatusCodes.Status500InternalServerError);

        return app;
    }
}