namespace SharedKernel.Models;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;

/// <summary>
/// Generic container for a single page of results, including paging metadata.
/// </summary>
public class PagedResult<T>
{
    public List<T> Items { get; }
    public int PageNumber { get; }
    public int TotalPages { get; }
    public int TotalCount { get; }
    public int PageSize { get; }
    public bool HasPreviousPage => PageNumber > 1;
    public bool HasNextPage => PageNumber < TotalPages;

    private PagedResult(List<T> items, int count, int pageNumber, int pageSize)
    {
        Items = items;
        TotalCount = count;
        PageSize = pageSize;
        PageNumber = pageNumber;
        TotalPages = (int)Math.Ceiling(count / (double)pageSize);
    }

    #region Sync Methods

    /// <summary>
    /// Synchronní vytvo<PERSON><PERSON><PERSON> str<PERSON><PERSON><PERSON><PERSON> výsledku s projekcí v paměti.
    /// </summary>
    public static PagedResult<T> Create<TSource>(
        IQueryable<TSource> source,
        int pageNumber,
        int pageSize,
        Func<TSource, T> map)
    {
        ValidatePaging(pageNumber, pageSize);
        // POZOR: EF Core bez OrderBy nemá garantované pořadí
        var totalCount = source.Count();
        var items = source
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToList()
            .Select(map)
            .ToList();
        return new PagedResult<T>(items, totalCount, pageNumber, pageSize);
    }

    /// <summary>
    /// Synchronní vytvoření stránkovaného výsledku s projekcí v SQL.
    /// </summary>
    public static PagedResult<T> Create<TSource>(
        IQueryable<TSource> source,
        int pageNumber,
        int pageSize,
        Expression<Func<TSource, T>> selector)
    {
        ValidatePaging(pageNumber, pageSize);
        // POZOR: EF Core bez OrderBy nemá garantované pořadí
        var totalCount = source.Count();
        var items = source
            .Select(selector)
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToList();
        return new PagedResult<T>(items, totalCount, pageNumber, pageSize);
    }

    #endregion

    #region Async Methods

    /// <summary>
    /// Asynchronní vytvoření stránkovaného výsledku s projekcí v paměti.
    /// </summary>
    public static async Task<PagedResult<T>> CreateAsync<TSource>(
        IQueryable<TSource> source,
        int pageNumber,
        int pageSize,
        Func<TSource, T> map,
        CancellationToken cancellationToken = default)
    {
        ValidatePaging(pageNumber, pageSize);
        var totalCount = await source.CountAsync(cancellationToken);
        var items = await source
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);
        var mapped = items.Select(map).ToList();
        return new PagedResult<T>(mapped, totalCount, pageNumber, pageSize);
    }

    /// <summary>
    /// Asynchronní vytvoření stránkovaného výsledku s projekcí v SQL.
    /// </summary>
    public static async Task<PagedResult<T>> CreateAsync<TSource>(
        IQueryable<TSource> source,
        int pageNumber,
        int pageSize,
        Expression<Func<TSource, T>> selector,
        CancellationToken cancellationToken = default)
    {
        ValidatePaging(pageNumber, pageSize);
        var totalCount = await source.CountAsync(cancellationToken);
        var items = await source
            .Select(selector)
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);
        return new PagedResult<T>(items, totalCount, pageNumber, pageSize);
    }

    #endregion

    #region Helpers

    private static void ValidatePaging(int pageNumber, int pageSize)
    {
        if (pageNumber < 1)
            throw new ArgumentOutOfRangeException(nameof(pageNumber), "PageNumber must be >= 1.");
        if (pageSize < 1)
            throw new ArgumentOutOfRangeException(nameof(pageSize), "PageSize must be > 0.");
    }

    #endregion
}
