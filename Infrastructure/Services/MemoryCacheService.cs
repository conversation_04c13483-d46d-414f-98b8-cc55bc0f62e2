using Application.Abstraction;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Primitives;
using System.Collections.Concurrent;

namespace Infrastructure.Services;

public class MemoryCacheService : ICacheService
{
    private readonly IMemoryCache _memoryCache;
    private CancellationTokenSource _resetCacheToken = new();

    // Sledování tagů - mapování tag -> seznam klíčů
    private readonly ConcurrentDictionary<string, ConcurrentBag<string>> _tagToKeys = new();
    // Sledován<PERSON> klí<PERSON> - mapování klíč -> seznam tagů
    private readonly ConcurrentDictionary<string, ConcurrentBag<string>> _keyToTags = new();

    public MemoryCacheService(IMemoryCache memoryCache)
    {
        _memoryCache = memoryCache;
    }

    public Task<T?> GetAsync<T>(string key, CancellationToken cancellationToken = default)
    {
        if (_memoryCache.TryGetValue(key, out T? value))
            return Task.FromResult(value);
        return Task.FromResult<T?>(default);
    }

    public Task SetAsync<T>(string key, T value, TimeSpan expiration, CancellationToken cancellationToken = default)
    {
        return SetAsync(key, value, expiration, null, cancellationToken);
    }

    public Task SetAsync<T>(string key, T value, TimeSpan expiration, IEnumerable<string>? tags, CancellationToken cancellationToken = default)
    {
        var options = new MemoryCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = expiration
            }
            // Přihlásíme položku na token, abychom ji mohli hromadně zrušit
            .AddExpirationToken(new CancellationChangeToken(_resetCacheToken.Token));

        _memoryCache.Set(key, value, options);

        // Registrujeme tagy pro tento klíč
        if (tags != null)
        {
            var tagList = tags.ToList();
            _keyToTags.AddOrUpdate(key, new ConcurrentBag<string>(tagList), (k, existing) =>
            {
                foreach (var tag in tagList)
                    existing.Add(tag);
                return existing;
            });

            foreach (var tag in tagList)
            {
                _tagToKeys.AddOrUpdate(tag, new ConcurrentBag<string> { key }, (t, existing) =>
                {
                    existing.Add(key);
                    return existing;
                });
            }
        }

        return Task.CompletedTask;
    }

    public Task RemoveAsync(string key, CancellationToken cancellationToken = default)
    {
        _memoryCache.Remove(key);

        // Odstraníme také mapování tagů
        if (_keyToTags.TryRemove(key, out var tags))
        {
            foreach (var tag in tags)
            {
                if (_tagToKeys.TryGetValue(tag, out var keys))
                {
                    // Odstraníme klíč ze seznamu klíčů pro tento tag
                    // Poznámka: ConcurrentBag nepodporuje Remove, takže vytvoříme nový bez tohoto klíče
                    var newKeys = new ConcurrentBag<string>(keys.Where(k => k != key));
                    _tagToKeys.TryUpdate(tag, newKeys, keys);
                }
            }
        }

        return Task.CompletedTask;
    }

    public Task RemoveByTagAsync(string tag, CancellationToken cancellationToken = default)
    {
        if (_tagToKeys.TryRemove(tag, out var keys))
        {
            foreach (var key in keys)
            {
                _memoryCache.Remove(key);
                _keyToTags.TryRemove(key, out _);
            }
        }

        return Task.CompletedTask;
    }

    public Task ClearAsync(CancellationToken cancellationToken = default)
    {
        // Zrušíme všechny položky registrované na starý token
        _resetCacheToken.Cancel();
        // A připravíme nový pro další ukládání
        _resetCacheToken = new CancellationTokenSource();

        // Vyčistíme také mapování tagů
        _tagToKeys.Clear();
        _keyToTags.Clear();

        return Task.CompletedTask;
    }
}