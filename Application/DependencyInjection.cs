using Application.Abstraction;
using Application.Abstraction.Mediator;
using Application.Features.Generic.Commands;
using Application.Features.Generic.Queries;
using Application.Features.Sample;
using Application.Features.Sample.Queries;
using Application.Models;
using Application.Pipeline;
using Application.Services;
using Application.Services.Mapper;
using Domain;
using Domain.Entities;
using Microsoft.Extensions.DependencyInjection;
using SharedKernel.Models;
using System.Reflection;
using SharedKernel.Abstractions;

namespace Application;

public static class DependencyInjection
{
    public static IServiceCollection AddApplicationServices(this IServiceCollection services)
    {
        services.AddTransient(typeof(IPipelineBehavior<,>), typeof(CacheBehavior<,>));
        services.AddTransient(typeof(IPipelineBehavior<,>), typeof(PerformanceBehavior<,>));

        // Automatická registrace generických handlerů
        RegisterGenericHandlers(services);

        return services;
    }



    /// <summary>
    /// Automaticky registruje generické handlery pro všechny entity
    /// </summary>
    private static void RegisterGenericHandlers(IServiceCollection services)
    {
        // Získání všech entit, které dědí z BaseEntity
        var entityTypes = GetEntityTypes();

        foreach (var entityInfo in entityTypes)
        {
            RegisterHandlersForEntity(services, entityInfo);
        }
    }

    /// <summary>
    /// Získá informace o všech entitách v projektu
    /// </summary>
    private static List<EntityInfo> GetEntityTypes()
    {
        var entityInfos = new List<EntityInfo>();

        // Prozatím registrujeme pouze SampleEntity
        // V budoucnu můžeme rozšířit o automatické hledání všech entit
        entityInfos.Add(new EntityInfo
        {
            EntityType = typeof(SampleEntity),
            KeyType = typeof(int),
            DtoType = typeof(SampleDto),
            AddEditType = typeof(SampleAddEdit)
        });

        return entityInfos;
    }

    /// <summary>
    /// Registruje handlery pro konkrétní entitu
    /// </summary>
    private static void RegisterHandlersForEntity(IServiceCollection services, EntityInfo entityInfo)
    {
        // Registrace Query handlerů
        RegisterQueryHandlers(services, entityInfo);

        // Registrace Command handlerů
        RegisterCommandHandlers(services, entityInfo);
    }

    /// <summary>
    /// Registruje query handlery pro entitu
    /// </summary>
    private static void RegisterQueryHandlers(IServiceCollection services, EntityInfo entityInfo)
    {
        // GetAllEntitiesQuery handler
        var getAllQueryType = typeof(GetAllEntitiesQuery<>).MakeGenericType(entityInfo.DtoType);
        var getAllResponseType = typeof(Result<>).MakeGenericType(typeof(List<>).MakeGenericType(entityInfo.DtoType));
        var getAllHandlerType = typeof(GetAllEntitiesQueryHandler<,>).MakeGenericType(entityInfo.EntityType, entityInfo.DtoType);
        var getAllRequestHandlerType = typeof(IRequestHandler<,>).MakeGenericType(getAllQueryType, getAllResponseType);

        services.AddScoped(getAllRequestHandlerType, getAllHandlerType);

        // GetEntityByIdQuery handler
        var getByIdQueryType = typeof(GetEntityByIdQuery<>).MakeGenericType(entityInfo.DtoType);
        var getByIdCachedQueryType = typeof(GetEntityByIdCachedQuery<>).MakeGenericType(entityInfo.DtoType);
        var getByIdResponseType = typeof(Result<>).MakeGenericType(entityInfo.DtoType);
        var getByIdHandlerType = typeof(GetEntityByIdQueryHandler<,>).MakeGenericType(entityInfo.EntityType, entityInfo.DtoType);
        var getByIdRequestHandlerType = typeof(IRequestHandler<,>).MakeGenericType(getByIdQueryType, getByIdResponseType);

        services.AddScoped(getByIdRequestHandlerType, getByIdHandlerType);
    }

    /// <summary>
    /// Registruje command handlery pro entitu
    /// </summary>
    private static void RegisterCommandHandlers(IServiceCollection services, EntityInfo entityInfo)
    {
        // CreateEntityCommand handler
        var createCommandType = typeof(CreateEntityCommand<,,>).MakeGenericType(entityInfo.EntityType, entityInfo.AddEditType, entityInfo.KeyType);
        var createResponseType = typeof(Result<>).MakeGenericType(entityInfo.KeyType);
        var createHandlerType = typeof(CreateEntityCommandHandler<,,>).MakeGenericType(entityInfo.EntityType, entityInfo.AddEditType, entityInfo.KeyType);
        var createRequestHandlerType = typeof(IRequestHandler<,>).MakeGenericType(createCommandType, createResponseType);

        services.AddScoped(createRequestHandlerType, createHandlerType);

        // UpdateEntityCommand handler
        var updateCommandType = typeof(UpdateEntityCommand<,,>).MakeGenericType(entityInfo.EntityType, entityInfo.AddEditType, entityInfo.KeyType);
        var updateResponseType = typeof(Result<bool>);
        var updateHandlerType = typeof(UpdateEntityCommandHandler<,,>).MakeGenericType(entityInfo.EntityType, entityInfo.AddEditType, entityInfo.KeyType);
        var updateRequestHandlerType = typeof(IRequestHandler<,>).MakeGenericType(updateCommandType, updateResponseType);

        services.AddScoped(updateRequestHandlerType, updateHandlerType);

        // DeleteEntityCommand handler
        var deleteCommandType = typeof(DeleteEntityCommand<,>).MakeGenericType(entityInfo.EntityType, entityInfo.KeyType);
        var deleteResponseType = typeof(Result<bool>);
        var deleteHandlerType = typeof(DeleteEntityCommandHandler<,>).MakeGenericType(entityInfo.EntityType, entityInfo.KeyType);
        var deleteRequestHandlerType = typeof(IRequestHandler<,>).MakeGenericType(deleteCommandType, deleteResponseType);

        services.AddScoped(deleteRequestHandlerType, deleteHandlerType);
    }

    /// <summary>
    /// Pomocná třída pro informace o entitě
    /// </summary>
    private class EntityInfo
    {
        public Type EntityType { get; set; } = null!;
        public Type KeyType { get; set; } = null!;
        public Type DtoType { get; set; } = null!;
        public Type AddEditType { get; set; } = null!;
    }
}