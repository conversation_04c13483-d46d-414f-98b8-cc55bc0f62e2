using Application.Abstraction;
using Application.Abstraction.Mediator;
using Application.Features.Generic;

namespace Application.Pipeline;

public class CacheBehavior<TRequest, TResponse> : IPipelineBehavior<TRequest, TResponse>
    where TRequest : ICachableQuery<TResponse>, IRequest<TResponse>
{
    private readonly ICacheService _cacheService;

    public CacheBehavior(ICacheService cacheService)
    {
        _cacheService = cacheService;
    }

    public async Task<TResponse> Handle(
        TRequest request,
        RequestHandlerDelegate<TResponse> next,
        CancellationToken cancellationToken)
    {
        // Pokud není cache klíč nastaven, přeskočíme cache
        if (string.IsNullOrEmpty(request.CacheKey))
        {
            return await next();
        }

        // Zkusíme zeptat cache
        var cached = await _cacheService.GetAsync<TResponse>(request.CacheKey, cancellationToken);
        if (cached is not null)
            return cached;

        // <PERSON><PERSON> zavol<PERSON> handler a ul<PERSON><PERSON><PERSON><PERSON> výsledek
        var response = await next();
        await _cacheService.SetAsync(request.CacheKey, response, TimeSpan.FromMinutes(10), request.Tags, cancellationToken);
        return response;
    }
}
