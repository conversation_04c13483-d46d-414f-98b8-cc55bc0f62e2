using Application.Features.Generic;
using Application.Features.Generic.Queries;
using Application.Features.Sample;
using Application.Models;
using Domain;
using Domain.Entities;
using SharedKernel.Models;

namespace Application.Features.Sample.Queries;

/// <summary>
/// Dotaz pro získání seznamu všech vzorových entit s podporou cache
/// </summary>
public class GetAllSamplesQuery : GetAllEntitiesQuery<SampleDto>
{
    /// <summary>
    /// Konstruktor s možností nastavení cache
    /// </summary>
    /// <param name="useCache">Ur<PERSON><PERSON>je, zda má být použita cache</param>
    public GetAllSamplesQuery(bool useCache = true)
    {
        UseCache = useCache;
    }
}

// Handler je již implementován genericky v GetAllEntitiesQueryHandler
