using Application.Features.Generic.Queries;
using Application.Features.Sample;
using Domain.Entities;

namespace Application.Features.Sample.Queries;

/// <summary>
/// Dotaz pro získání vzorové entity podle ID s podporou cache
/// </summary>
public class GetSampleByIdQuery : GetEntityByIdQuery<SampleDto>
{
    /// <summary>
    /// Konstruktor s možností nastavení cache
    /// </summary>
    /// <param name="id">ID vzorové entity</param>
    /// <param name="useCache">Určuje, zda má být použita cache</param>
    public GetSampleByIdQuery(int id, bool useCache = true)
    {
        Id = id;
        UseCache = useCache;
    }
}

// Handler je již implementován genericky v GetEntityByIdQueryHandler
