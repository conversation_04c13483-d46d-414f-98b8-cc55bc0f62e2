using Application.Abstraction;
using Application.Abstraction.Mediator;
using Application.Services.Mapper;
using SharedKernel.Models;

namespace Application.Features.Generic.Queries;

/// <summary>
/// Generický dotaz pro získání entity podle ID s podporou cache
/// </summary>
/// <typeparam name="TDto">Typ DTO objektu</typeparam>
[Obsolete("Použijte GetEntityByIdQuery<TDto> s UseCache = true místo tohoto dotazu. Tento dotaz bude odstraněn v budouc<PERSON> verzi.")]
public class GetEntityByIdCachedQuery<TDto> : IRequest<Result<TDto>>, ICachableQuery<Result<TDto>>
{
    /// <summary>
    /// ID entity, která má být načtena
    /// </summary>
    public required int Id { get; set; }

    /// <summary>
    /// Klíč pro cache - obsahuje typ DTO a ID entity
    /// </summary>
    public string CacheKey => $"GetById_{typeof(TDto).Name}_{Id}";

    /// <summary>
    /// Tagy pro invalidaci cache
    /// </summary>
    public IEnumerable<string>? Tags => new[] { typeof(TDto).Name };
}

/// <summary>
/// Handler pro zpracování GetEntityByIdCachedQuery
/// </summary>
/// <typeparam name="TEntity">Typ entity</typeparam>
/// <typeparam name="TDto">Typ DTO objektu</typeparam>
[Obsolete("Použijte GetEntityByIdQueryHandler<TEntity, TDto> s UseCache = true místo tohoto handleru. Tento handler bude odstraněn v budoucí verzi.")]
public class GetEntityByIdCachedQueryHandler<TEntity, TDto>
    : IRequestHandler<GetEntityByIdCachedQuery<TDto>, Result<TDto>>
    where TEntity : class
    where TDto : class, new()
{
    private readonly IApplicationDbContext _context;
    private readonly IUnifiedMapper<TEntity, TDto> _mapper;

    /// <summary>
    /// Inicializuje novou instanci handleru
    /// </summary>
    /// <param name="context">Databázový kontext</param>
    /// <param name="mapper">Mapper pro převod mezi entitou a DTO</param>
    public GetEntityByIdCachedQueryHandler(IApplicationDbContext context, IUnifiedMapper<TEntity, TDto> mapper)
    {
        _context = context;
        _mapper = mapper;
    }

    /// <summary>
    /// Zpracuje dotaz pro získání entity podle ID
    /// </summary>
    /// <param name="request">Dotaz obsahující ID entity</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Result s DTO objektem nebo chybou</returns>
    public async Task<Result<TDto>> Handle(GetEntityByIdCachedQuery<TDto> request, CancellationToken cancellationToken)
    {
        try
        {
            var entity = await _context.Set<TEntity>()
                .FindAsync(new object[] { request.Id }, cancellationToken);

            if (entity is null)
                return Result<TDto>.Error("NotFound", $"{typeof(TEntity).Name} s ID {request.Id} nebyl nalezen.");

            var dto = _mapper.Map(entity);
            return await Result<TDto>.OkAsync(dto);
        }
        catch (Exception ex)
        {
            return await Result<TDto>.ErrorAsync($"Chyba při načítání entity: {ex.Message}");
        }
    }
}
