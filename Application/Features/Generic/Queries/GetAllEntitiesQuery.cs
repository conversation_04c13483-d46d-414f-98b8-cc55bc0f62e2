using Application.Abstraction;
using Application.Abstraction.Mediator;
using Application.Exceptions;
using Application.Features.Generic;
using Application.Models;
using Application.Services;
using Application.Services.Mapper;
using Microsoft.EntityFrameworkCore;
using SharedKernel.Models;

namespace Application.Features.Generic.Queries;

/// <summary>
/// Generický dotaz pro získání všech entit s volitelnou podporou cache
/// </summary>
/// <typeparam name="TDto">Typ DTO objektu</typeparam>
public class GetAllEntitiesQuery<TDto> : IRequest<Result<List<TDto>>>, ICachableQuery<Result<List<TDto>>>
{
    /// <summary>
    /// Název entity pro cache tagy - musí být nastaven handlerem
    /// </summary>
    public string? EntityName { get; set; }

    /// <summary>
    /// Určuje, zda má být použita cache pro tento dotaz
    /// </summary>
    public bool UseCache { get; set; } = false;

    /// <summary>
    /// Kl<PERSON>č pro cache - obsahuje typ DTO
    /// </summary>
    public string CacheKey => UseCache ? $"GetAll_{typeof(TDto).Name}" : string.Empty;

    /// <summary>
    /// Tagy pro invalidaci cache
    /// </summary>
    public IEnumerable<string>? Tags => UseCache && !string.IsNullOrEmpty(EntityName) ? new[] { EntityName } : null;
}

public class GetAllEntitiesQueryHandler<TEntity, TDto>
    : GenericCollectionQueryHandler<TEntity, TDto, GetAllEntitiesQuery<TDto>>
    where TEntity : class
    where TDto : class, new()
{
    public GetAllEntitiesQueryHandler(IApplicationDbContext context, IUnifiedMapper<TEntity, TDto> mapper)
        : base(context, mapper)
    {
    }

    protected override async Task<List<TDto>> FetchCollectionAsync(GetAllEntitiesQuery<TDto> request,
        CancellationToken cancellationToken)
    {
        var data = await Context.Set<TEntity>().ToListAsync(cancellationToken);
        if (data is null)
            throw new NotFoundException(typeof(TEntity).Name, typeof(TDto).Name);

        // Vracíme přímo kolekci TDto
        return Mapper.MapCollection(data).ToList();
    }
}