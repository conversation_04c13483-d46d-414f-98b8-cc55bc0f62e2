using Application.Abstraction;
using Application.Abstraction.Mediator;
using Application.Models;
using Application.Services.Mapper;
using System.Text;
using SharedKernel.Models;

namespace Application.Features.Generic.Queries;

/// <summary>
/// Základní dotaz pro stránkovací dotazy entit s volitelnou podporou cache
/// </summary>
/// <typeparam name="TDto">Typ DTO pro výstup</typeparam>
public class GetPagedEntitiesQuery<TDto> : IRequest<Result<PagedResult<TDto>>>, ICachableQuery<Result<PagedResult<TDto>>>
{
    /// <summary>
    /// Název entity pro cache tagy - musí být nastaven handlerem
    /// </summary>
    public string? EntityName { get; set; }
    private int _pageNumber = 1;
    private int _pageSize = 10;

    /// <summary>
    /// Číslo stránky (1 a více)
    /// </summary>
    public int PageNumber 
    { 
        get => _pageNumber;
        set => _pageNumber = value < 1 ? 1 : value;
    }
    
    /// <summary>
    /// Veli<PERSON><PERSON> stránky (1 až 100)
    /// </summary>
    public int PageSize 
    {
        get => _pageSize;
        set => _pageSize = value switch
        {
            < 1 => 10,
            > 100 => 100,
            _ => value
        };
    }

    /// <summary>
    /// Název vlastnosti podle které se má řadit
    /// </summary>
    public string? SortBy { get; set; }
    
    /// <summary>
    /// Příznak pro sestupné řazení
    /// </summary>
    public bool SortDescending { get; set; } = false;

    /// <summary>
    /// Určuje, zda má být použita cache pro tento dotaz
    /// </summary>
    public bool UseCache { get; set; } = false;
    
    /// <summary>
    /// Implementace ICachableQuery - klíč pro cache
    /// </summary>
    public virtual string CacheKey
    {
        get
        {
            if (!UseCache)
                return string.Empty;

            var key = new StringBuilder($"GetPaged_{typeof(TDto).Name}");
            key.Append($"_Page{PageNumber}");
            key.Append($"_Size{PageSize}");

            if (!string.IsNullOrEmpty(SortBy))
            {
                key.Append($"_Sort{SortBy}");
                if (SortDescending)
                    key.Append("Desc");
            }

            return key.ToString();
        }
    }

    /// <summary>
    /// Implementace ICachableQuery - tagy pro invalidaci cache
    /// </summary>
    public virtual IEnumerable<string>? Tags => UseCache && !string.IsNullOrEmpty(EntityName) ? new[] { EntityName } : null;
}

/// <summary>
/// Obecný handler pro zpracování stránkovacích dotazů
/// </summary>
public class GetPagedEntitiesQueryHandler<TEntity, TDto>
    : GenericPagedQueryHandler<TEntity, TDto, GetPagedEntitiesQuery<TDto>>
    where TEntity : class
    where TDto : class, new()
{
    public GetPagedEntitiesQueryHandler(IApplicationDbContext context, IUnifiedMapper<TEntity, TDto> mapper)
        : base(context, mapper)
    {
    }

    protected override async Task<PagedResult<TDto>> FetchPagedAsync(GetPagedEntitiesQuery<TDto> request,
        CancellationToken cancellationToken)
    {
        var query = Context.Set<TEntity>().AsQueryable();

        // Aplikace filtrů - přetížit v konkrétních implementacích
        query = ApplyFilters(query, request);

        // Aplikace řazení
        try
        {
            query = ApplySorting(query, request);
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"Chyba při řazení: {ex.Message}", ex);
        }

        // Aplikace eager loadingu - přetížit v konkrétních implementacích
        query = ApplyIncludes(query, request);

        var pagedList = await PagedResult<TDto>.CreateAsync(
            query,
            request.PageNumber,
            request.PageSize,
            (Func<TEntity, TDto>)(entity => Mapper.Map(entity)),
            cancellationToken);

        return pagedList;
    }
    
    /// <summary>
    /// Aplikuje filtry na dotaz. Výchozí implementace neaplikuje žádné filtry.
    /// Přetižte tuto metodu v odvozené třídě pro implementaci specifických filtrů.
    /// </summary>
    protected virtual IQueryable<TEntity> ApplyFilters(
        IQueryable<TEntity> query,
        GetPagedEntitiesQuery<TDto> request)
    {
        return query;
    }
    
    /// <summary>
    /// Aplikuje řazení na dotaz podle zadaného názvu vlastnosti
    /// </summary>
    protected virtual IQueryable<TEntity> ApplySorting(
        IQueryable<TEntity> query,
        GetPagedEntitiesQuery<TDto> request)
    {
        // Pokud není zadán název vlastnosti pro řazení, vrátíme dotaz beze změny
        if (string.IsNullOrEmpty(request.SortBy))
            return query;

        // Zjistíme, zda entita obsahuje vlastnost s daným názvem
        var propertyInfo = typeof(TEntity).GetProperty(request.SortBy);
        if (propertyInfo == null)
            throw new InvalidOperationException($"Vlastnost '{request.SortBy}' nebyla nalezena na entitě {typeof(TEntity).Name}");

        // Vytvoříme lambda výraz pro řazení
        var parameter = System.Linq.Expressions.Expression.Parameter(typeof(TEntity), "x");
        var property = System.Linq.Expressions.Expression.Property(parameter, propertyInfo);
        var lambda = System.Linq.Expressions.Expression.Lambda(property, parameter);

        // Aplikujeme řazení (vzestupné nebo sestupné)
        var methodName = request.SortDescending ? "OrderByDescending" : "OrderBy";
        var resultExp = System.Linq.Expressions.Expression.Call(
            typeof(Queryable),
            methodName,
            new[] { typeof(TEntity), propertyInfo.PropertyType },
            query.Expression,
            System.Linq.Expressions.Expression.Quote(lambda));

        return query.Provider.CreateQuery<TEntity>(resultExp);
    }
    
    /// <summary>
    /// Aplikuje eager loading na dotaz. Výchozí implementace nepřidává žádné include.
    /// Přetižte tuto metodu v odvozené třídě pro načtení souvisejících entit.
    /// </summary>
    protected virtual IQueryable<TEntity> ApplyIncludes(
        IQueryable<TEntity> query,
        GetPagedEntitiesQuery<TDto> request)
    {
        return query;
    }
}
