using Application.Abstraction;
using Application.Abstraction.Mediator;
using Application.Features.Generic.Queries;
using Application.Services.Mapper;
using SharedKernel.Models;

namespace Application.Features.Generic;

// 1) Generický handler pro vracení kolekce TDto
public abstract class GenericCollectionQueryHandler<TEntity, TDto, TRequest>
    : IRequestHandler<TRequest, Result<List<TDto>>>
    where TRequest : IRequest<Result<List<TDto>>>
    where TEntity : class
    where TDto : class, new()
{
    protected readonly IApplicationDbContext Context;
    protected readonly IUnifiedMapper<TEntity, TDto> Mapper;

    protected GenericCollectionQueryHandler(IApplicationDbContext context, IUnifiedMapper<TEntity, TDto> mapper)
    {
        Context = context;
        Mapper = mapper;
    }

    public async Task<Result<List<TDto>>> Handle(TRequest request, CancellationToken cancellationToken)
    {
        // Nastavíme EntityName pro cache tagy
        if (request is GetAllEntitiesQuery<TDto> getAllQuery)
        {
            getAllQuery.EntityName = typeof(TEntity).Name;
        }

        var data = await FetchCollectionAsync(request, cancellationToken);
        return await Result<List<TDto>>.OkAsync(data);
    }

    protected abstract Task<List<TDto>> FetchCollectionAsync(TRequest request, CancellationToken cancellationToken);
}

// 2) Generický handler pro vracení jednoho TDto
public abstract class GenericSingleQueryHandler<TEntity, TDto, TRequest>
    : IRequestHandler<TRequest, Result<TDto>>
    where TRequest : IRequest<Result<TDto>>
    where TEntity : class
    where TDto : class, new()
{
    protected readonly IApplicationDbContext Context;
    protected readonly IUnifiedMapper<TEntity, TDto> Mapper;

    protected GenericSingleQueryHandler(IApplicationDbContext context, IUnifiedMapper<TEntity, TDto> mapper)
    {
        Context = context;
        Mapper = mapper;
    }

    public async Task<Result<TDto>> Handle(TRequest request, CancellationToken cancellationToken)
    {
        // Nastavíme EntityName pro cache tagy
        if (request is GetEntityByIdQuery<TDto> getByIdQuery)
        {
            getByIdQuery.EntityName = typeof(TEntity).Name;
        }

        var item = await FetchSingleAsync(request, cancellationToken);
        return await Result<TDto>.OkAsync(item);
    }

    protected abstract Task<TDto> FetchSingleAsync(TRequest request, CancellationToken cancellationToken);
}

// 3) Generický handler pro vracení stránkovaných výsledků
public abstract class GenericPagedQueryHandler<TEntity, TDto, TRequest>
    : IRequestHandler<TRequest, Result<PagedResult<TDto>>>
    where TRequest : IRequest<Result<PagedResult<TDto>>>
    where TEntity : class
    where TDto : class, new()
{
    protected readonly IApplicationDbContext Context;
    protected readonly IUnifiedMapper<TEntity, TDto> Mapper;

    protected GenericPagedQueryHandler(IApplicationDbContext context, IUnifiedMapper<TEntity, TDto> mapper)
    {
        Context = context;
        Mapper = mapper;
    }

    public async Task<Result<PagedResult<TDto>>> Handle(TRequest request, CancellationToken cancellationToken)
    {
        try
        {
            // Nastavíme EntityName pro cache tagy
            if (request is GetPagedEntitiesQuery<TDto> getPagedQuery)
            {
                getPagedQuery.EntityName = typeof(TEntity).Name;
            }

            var pagedResult = await FetchPagedAsync(request, cancellationToken);
            return await Result<PagedResult<TDto>>.OkAsync(pagedResult);
        }
        catch (Exception ex)
        {
            return await Result<PagedResult<TDto>>.ErrorAsync($"Chyba při získávání stránkovaných dat: {ex.Message}");
        }
    }

    protected abstract Task<PagedResult<TDto>> FetchPagedAsync(TRequest request, CancellationToken cancellationToken);
}