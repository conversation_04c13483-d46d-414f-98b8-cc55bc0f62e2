
using Application.Abstraction.Mediator;

namespace Application.Features.Generic;

public interface IQuery<TResult> : IRequest<TResult>;

public interface ICachableQuery<TResult>
{
    /// <summary>
    /// Jedinečný klíč pro cache (např. "GetUserById:1234").
    /// </summary>
    string Cache<PERSON><PERSON> { get; }

    /// <summary>
    /// Tagy pro invalidaci cache
    /// </summary>
    IEnumerable<string>? Tags { get; }
}

public interface IInvalidateCache
{
    /// <summary>
    /// Jeden nebo více klí<PERSON>, které se mají po provedení příkazu vymazat.
    /// </summary>
    IEnumerable<string> CacheKeys { get; }

    /// <summary>
    /// Jeden nebo více tagů, podle kterých se mají po provedení příkazu vymazat cache záznamy.
    /// </summary>
    IEnumerable<string>? CacheTags { get; }
}