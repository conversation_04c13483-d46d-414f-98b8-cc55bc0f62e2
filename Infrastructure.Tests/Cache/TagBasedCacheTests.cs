using Infrastructure.Services;
using Microsoft.Extensions.Caching.Memory;
using Xunit;

namespace Infrastructure.Tests.Cache;

/// <summary>
/// Testy pro tag-based cache invalidation v MemoryCacheService
/// </summary>
public class TagBasedCacheTests
{
    [Fact]
    public async Task SetAsync_WithTags_ShouldStoreValueAndTags()
    {
        // Arrange
        var memoryCache = new MemoryCache(new MemoryCacheOptions());
        var cacheService = new MemoryCacheService(memoryCache);
        var key = "test-key";
        var value = "test-value";
        var tags = new[] { "tag1", "tag2" };

        // Act
        await cacheService.SetAsync(key, value, TimeSpan.FromMinutes(10), tags);

        // Assert
        var cachedValue = await cacheService.GetAsync<string>(key);
        Assert.Equal(value, cachedValue);
    }

    [Fact]
    public async Task RemoveByTagAsync_ShouldRemoveAllKeysWithTag()
    {
        // Arrange
        var memoryCache = new MemoryCache(new MemoryCacheOptions());
        var cacheService = new MemoryCacheService(memoryCache);
        
        // <PERSON><PERSON>ž<PERSON><PERSON> něko<PERSON> hodnot se stejným tagem
        await cacheService.SetAsync("key1", "value1", TimeSpan.FromMinutes(10), new[] { "common-tag", "tag1" });
        await cacheService.SetAsync("key2", "value2", TimeSpan.FromMinutes(10), new[] { "common-tag", "tag2" });
        await cacheService.SetAsync("key3", "value3", TimeSpan.FromMinutes(10), new[] { "different-tag" });

        // Ověříme, že jsou uložené
        Assert.Equal("value1", await cacheService.GetAsync<string>("key1"));
        Assert.Equal("value2", await cacheService.GetAsync<string>("key2"));
        Assert.Equal("value3", await cacheService.GetAsync<string>("key3"));

        // Act - odstraníme podle tagu
        await cacheService.RemoveByTagAsync("common-tag");

        // Assert
        Assert.Null(await cacheService.GetAsync<string>("key1"));
        Assert.Null(await cacheService.GetAsync<string>("key2"));
        Assert.Equal("value3", await cacheService.GetAsync<string>("key3")); // Tato by měla zůstat
    }

    [Fact]
    public async Task RemoveAsync_ShouldRemoveKeyAndUpdateTagMappings()
    {
        // Arrange
        var memoryCache = new MemoryCache(new MemoryCacheOptions());
        var cacheService = new MemoryCacheService(memoryCache);
        
        await cacheService.SetAsync("key1", "value1", TimeSpan.FromMinutes(10), new[] { "tag1", "tag2" });
        await cacheService.SetAsync("key2", "value2", TimeSpan.FromMinutes(10), new[] { "tag1" });

        // Act - odstraníme key1
        await cacheService.RemoveAsync("key1");

        // Assert
        Assert.Null(await cacheService.GetAsync<string>("key1"));
        Assert.Equal("value2", await cacheService.GetAsync<string>("key2"));

        // Když nyní odstraníme podle tag1, měl by se odstranit pouze key2
        await cacheService.RemoveByTagAsync("tag1");
        Assert.Null(await cacheService.GetAsync<string>("key2"));
    }

    [Fact]
    public async Task SetAsync_WithoutTags_ShouldWork()
    {
        // Arrange
        var memoryCache = new MemoryCache(new MemoryCacheOptions());
        var cacheService = new MemoryCacheService(memoryCache);

        // Act
        await cacheService.SetAsync("key", "value", TimeSpan.FromMinutes(10));

        // Assert
        var cachedValue = await cacheService.GetAsync<string>("key");
        Assert.Equal("value", cachedValue);
    }

    [Fact]
    public async Task SetAsync_WithNullTags_ShouldWork()
    {
        // Arrange
        var memoryCache = new MemoryCache(new MemoryCacheOptions());
        var cacheService = new MemoryCacheService(memoryCache);

        // Act
        await cacheService.SetAsync("key", "value", TimeSpan.FromMinutes(10), null);

        // Assert
        var cachedValue = await cacheService.GetAsync<string>("key");
        Assert.Equal("value", cachedValue);
    }

    [Fact]
    public async Task RemoveByTagAsync_WithNonExistentTag_ShouldNotThrow()
    {
        // Arrange
        var memoryCache = new MemoryCache(new MemoryCacheOptions());
        var cacheService = new MemoryCacheService(memoryCache);

        // Act & Assert - nemělo by hodit výjimku
        await cacheService.RemoveByTagAsync("non-existent-tag");
    }

    [Fact]
    public async Task ClearAsync_ShouldClearTagMappings()
    {
        // Arrange
        var memoryCache = new MemoryCache(new MemoryCacheOptions());
        var cacheService = new MemoryCacheService(memoryCache);
        
        await cacheService.SetAsync("key1", "value1", TimeSpan.FromMinutes(10), new[] { "tag1" });
        await cacheService.SetAsync("key2", "value2", TimeSpan.FromMinutes(10), new[] { "tag2" });

        // Act
        await cacheService.ClearAsync();

        // Assert
        Assert.Null(await cacheService.GetAsync<string>("key1"));
        Assert.Null(await cacheService.GetAsync<string>("key2"));

        // Přidáme nové hodnoty se stejnými tagy - neměly by být ovlivněny starými mapováními
        await cacheService.SetAsync("key3", "value3", TimeSpan.FromMinutes(10), new[] { "tag1" });
        await cacheService.RemoveByTagAsync("tag1");
        Assert.Null(await cacheService.GetAsync<string>("key3"));
    }

    [Fact]
    public async Task MultipleKeysWithSameTag_RemoveByTag_ShouldRemoveAll()
    {
        // Arrange
        var memoryCache = new MemoryCache(new MemoryCacheOptions());
        var cacheService = new MemoryCacheService(memoryCache);
        
        // Uložíme více klíčů se stejným tagem
        for (int i = 0; i < 5; i++)
        {
            await cacheService.SetAsync($"key{i}", $"value{i}", TimeSpan.FromMinutes(10), new[] { "common-tag" });
        }

        // Ověříme, že jsou všechny uložené
        for (int i = 0; i < 5; i++)
        {
            Assert.Equal($"value{i}", await cacheService.GetAsync<string>($"key{i}"));
        }

        // Act
        await cacheService.RemoveByTagAsync("common-tag");

        // Assert
        for (int i = 0; i < 5; i++)
        {
            Assert.Null(await cacheService.GetAsync<string>($"key{i}"));
        }
    }
}
