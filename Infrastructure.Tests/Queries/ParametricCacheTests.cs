using Application.Features.Generic.Queries;
using Application.Features.Sample;
using Xunit;

namespace Infrastructure.Tests.Queries;

/// <summary>
/// Testy pro parametrickou cache funkcionalitu v generických dotazech
/// </summary>
public class ParametricCacheTests
{
    [Fact]
    public void GetEntityByIdQuery_WithCacheEnabled_ShouldHaveCacheKey()
    {
        // Arrange
        var query = new GetEntityByIdQuery<SampleDto>
        {
            Id = 123,
            UseCache = true
        };

        // Nastavíme EntityName manuálně pro test
        query.EntityName = "SampleEntity";

        // Act & Assert
        Assert.Equal("GetById_SampleDto_123", query.CacheKey);
        Assert.NotNull(query.Tags);
        Assert.Contains("SampleEntity", query.Tags);
    }

    [Fact]
    public void GetEntityByIdQuery_WithCacheDisabled_ShouldHaveEmptyCacheKey()
    {
        // Arrange
        var query = new GetEntityByIdQuery<SampleDto>
        {
            Id = 123,
            UseCache = false
        };

        // Act & Assert
        Assert.Equal(string.Empty, query.CacheKey);
        Assert.Null(query.Tags);
    }

    [Fact]
    public void GetAllEntitiesQuery_WithCacheEnabled_ShouldHaveCacheKey()
    {
        // Arrange
        var query = new GetAllEntitiesQuery<SampleDto>
        {
            UseCache = true
        };

        // Nastavíme EntityName manuálně pro test
        query.EntityName = "SampleEntity";

        // Act & Assert
        Assert.Equal("GetAll_SampleDto", query.CacheKey);
        Assert.NotNull(query.Tags);
        Assert.Contains("SampleEntity", query.Tags);
    }

    [Fact]
    public void GetAllEntitiesQuery_WithCacheDisabled_ShouldHaveEmptyCacheKey()
    {
        // Arrange
        var query = new GetAllEntitiesQuery<SampleDto>
        {
            UseCache = false
        };

        // Act & Assert
        Assert.Equal(string.Empty, query.CacheKey);
        Assert.Null(query.Tags);
    }

    [Fact]
    public void GetPagedEntitiesQuery_WithCacheEnabled_ShouldHaveCacheKey()
    {
        // Arrange
        var query = new GetPagedEntitiesQuery<SampleDto>
        {
            PageNumber = 2,
            PageSize = 20,
            SortBy = "Name",
            SortDescending = true,
            UseCache = true
        };

        // Nastavíme EntityName manuálně pro test
        query.EntityName = "SampleEntity";

        // Act & Assert
        Assert.Equal("GetPaged_SampleDto_Page2_Size20_SortNameDesc", query.CacheKey);
        Assert.NotNull(query.Tags);
        Assert.Contains("SampleEntity", query.Tags);
    }

    [Fact]
    public void GetPagedEntitiesQuery_WithCacheDisabled_ShouldHaveEmptyCacheKey()
    {
        // Arrange
        var query = new GetPagedEntitiesQuery<SampleDto>
        {
            PageNumber = 2,
            PageSize = 20,
            SortBy = "Name",
            SortDescending = true,
            UseCache = false
        };

        // Act & Assert
        Assert.Equal(string.Empty, query.CacheKey);
        Assert.Null(query.Tags);
    }

    [Fact]
    public void GetPagedEntitiesQuery_DefaultUseCache_ShouldBeFalse()
    {
        // Arrange & Act
        var query = new GetPagedEntitiesQuery<SampleDto>();

        // Assert
        Assert.False(query.UseCache);
        Assert.Equal(string.Empty, query.CacheKey);
    }

    [Fact]
    public void GetEntityByIdQuery_DefaultUseCache_ShouldBeFalse()
    {
        // Arrange & Act
        var query = new GetEntityByIdQuery<SampleDto> { Id = 1 };

        // Assert
        Assert.False(query.UseCache);
        Assert.Equal(string.Empty, query.CacheKey);
    }

    [Fact]
    public void GetAllEntitiesQuery_DefaultUseCache_ShouldBeFalse()
    {
        // Arrange & Act
        var query = new GetAllEntitiesQuery<SampleDto>();

        // Assert
        Assert.False(query.UseCache);
        Assert.Equal(string.Empty, query.CacheKey);
    }
}
