using Application.Abstraction;
using Application.Features.Generic.Commands;
using Application.Features.Generic.Queries;
using Application.Features.Sample;
using Application.Abstraction.Mediator;
using Domain.Entities;
using Infrastructure.Services;
using Microsoft.Extensions.Caching.Memory;
using Moq;
using Xunit;
using SharedKernel.Models;

namespace Infrastructure.Tests.Integration;

/// <summary>
/// Integrační testy pro ověření, že cache invalidation funguje end-to-end
/// </summary>
public class CacheInvalidationIntegrationTests
{
    [Fact]
    public async Task CreateCommand_ShouldInvalidateCacheByTag()
    {
        // Arrange
        var memoryCache = new MemoryCache(new MemoryCacheOptions());
        var cacheService = new MemoryCacheService(memoryCache);
        
        // Nejprve uložíme nějaké hodnoty do cache s tagem "SampleEntity"
        await cacheService.SetAsync("GetAll_SampleDto", "cached-list", TimeSpan.FromMinutes(10), new[] { "SampleEntity" });
        await cacheService.SetAsync("GetById_SampleDto_1", "cached-item", TimeSpan.FromMinutes(10), new[] { "SampleEntity" });
        await cacheService.SetAsync("GetPaged_SampleDto_Page1_Size10", "cached-page", TimeSpan.FromMinutes(10), new[] { "SampleEntity" });
        
        // Ověříme, že jsou v cache
        Assert.Equal("cached-list", await cacheService.GetAsync<string>("GetAll_SampleDto"));
        Assert.Equal("cached-item", await cacheService.GetAsync<string>("GetById_SampleDto_1"));
        Assert.Equal("cached-page", await cacheService.GetAsync<string>("GetPaged_SampleDto_Page1_Size10"));
        
        // Vytvoříme command, který by měl invalidovat cache
        var command = new CreateEntityCommand<SampleEntity, SampleDto, int>
        {
            Payload = new SampleDto { Name = "Test Sample" }
        };
        
        // Act - simulujeme invalidaci cache podle tagů
        foreach (var tag in command.CacheTags!)
        {
            await cacheService.RemoveByTagAsync(tag);
        }
        
        // Assert - všechny cache záznamy s tagem "SampleEntity" by měly být odstraněny
        Assert.Null(await cacheService.GetAsync<string>("GetAll_SampleDto"));
        Assert.Null(await cacheService.GetAsync<string>("GetById_SampleDto_1"));
        Assert.Null(await cacheService.GetAsync<string>("GetPaged_SampleDto_Page1_Size10"));
    }

    [Fact]
    public async Task UpdateCommand_ShouldInvalidateCacheByTag()
    {
        // Arrange
        var memoryCache = new MemoryCache(new MemoryCacheOptions());
        var cacheService = new MemoryCacheService(memoryCache);
        
        // Uložíme cache záznamy
        await cacheService.SetAsync("GetAll_SampleDto", "cached-list", TimeSpan.FromMinutes(10), new[] { "SampleEntity" });
        await cacheService.SetAsync("GetById_SampleDto_1", "cached-item", TimeSpan.FromMinutes(10), new[] { "SampleEntity" });
        await cacheService.SetAsync("GetAll_OtherDto", "other-cached-list", TimeSpan.FromMinutes(10), new[] { "OtherEntity" });
        
        var command = new UpdateEntityCommand<SampleEntity, SampleDto, int>
        {
            Id = 1,
            Payload = new SampleDto { Name = "Updated Sample" }
        };
        
        // Act
        foreach (var tag in command.CacheTags!)
        {
            await cacheService.RemoveByTagAsync(tag);
        }
        
        // Assert - pouze SampleEntity cache by měly být odstraněny
        Assert.Null(await cacheService.GetAsync<string>("GetAll_SampleDto"));
        Assert.Null(await cacheService.GetAsync<string>("GetById_SampleDto_1"));
        Assert.Equal("other-cached-list", await cacheService.GetAsync<string>("GetAll_OtherDto")); // Tato by měla zůstat
    }

    [Fact]
    public async Task DeleteCommand_ShouldInvalidateCacheByTag()
    {
        // Arrange
        var memoryCache = new MemoryCache(new MemoryCacheOptions());
        var cacheService = new MemoryCacheService(memoryCache);
        
        // Uložíme cache záznamy
        await cacheService.SetAsync("GetAll_SampleDto", "cached-list", TimeSpan.FromMinutes(10), new[] { "SampleEntity" });
        await cacheService.SetAsync("GetById_SampleDto_1", "cached-item", TimeSpan.FromMinutes(10), new[] { "SampleEntity" });
        await cacheService.SetAsync("GetPaged_SampleDto_Page1_Size10", "cached-page", TimeSpan.FromMinutes(10), new[] { "SampleEntity" });
        
        var command = new DeleteEntityCommand<SampleEntity, int>
        {
            Id = 1
        };
        
        // Act
        foreach (var tag in command.CacheTags!)
        {
            await cacheService.RemoveByTagAsync(tag);
        }
        
        // Assert
        Assert.Null(await cacheService.GetAsync<string>("GetAll_SampleDto"));
        Assert.Null(await cacheService.GetAsync<string>("GetById_SampleDto_1"));
        Assert.Null(await cacheService.GetAsync<string>("GetPaged_SampleDto_Page1_Size10"));
    }

    [Fact]
    public async Task ParametricCache_WithUseCache_ShouldGenerateCorrectTags()
    {
        // Arrange & Act
        var getAllQuery = new GetAllEntitiesQuery<SampleDto> { UseCache = true };
        var getByIdQuery = new GetEntityByIdQuery<SampleDto> { Id = 1, UseCache = true };
        var getPagedQuery = new GetPagedEntitiesQuery<SampleDto> { PageNumber = 1, PageSize = 10, UseCache = true };
        
        // Assert - všechny dotazy by měly mít správné tagy
        Assert.NotNull(getAllQuery.Tags);
        Assert.Contains("SampleDto", getAllQuery.Tags);
        
        Assert.NotNull(getByIdQuery.Tags);
        Assert.Contains("SampleDto", getByIdQuery.Tags);
        
        Assert.NotNull(getPagedQuery.Tags);
        Assert.Contains("SampleDto", getPagedQuery.Tags);
    }

    [Fact]
    public async Task ParametricCache_WithoutUseCache_ShouldNotGenerateTags()
    {
        // Arrange & Act
        var getAllQuery = new GetAllEntitiesQuery<SampleDto> { UseCache = false };
        var getByIdQuery = new GetEntityByIdQuery<SampleDto> { Id = 1, UseCache = false };
        var getPagedQuery = new GetPagedEntitiesQuery<SampleDto> { PageNumber = 1, PageSize = 10, UseCache = false };
        
        // Assert - žádné dotazy by neměly mít tagy
        Assert.Null(getAllQuery.Tags);
        Assert.Null(getByIdQuery.Tags);
        Assert.Null(getPagedQuery.Tags);
    }

    [Fact]
    public async Task CacheInvalidation_ShouldWorkWithMixedTags()
    {
        // Arrange
        var memoryCache = new MemoryCache(new MemoryCacheOptions());
        var cacheService = new MemoryCacheService(memoryCache);
        
        // Uložíme cache záznamy s různými tagy
        await cacheService.SetAsync("key1", "value1", TimeSpan.FromMinutes(10), new[] { "SampleDto", "CommonTag" });
        await cacheService.SetAsync("key2", "value2", TimeSpan.FromMinutes(10), new[] { "OtherDto", "CommonTag" });
        await cacheService.SetAsync("key3", "value3", TimeSpan.FromMinutes(10), new[] { "SampleDto" });
        await cacheService.SetAsync("key4", "value4", TimeSpan.FromMinutes(10), new[] { "OtherDto" });
        
        // Act - odstraníme podle tagu "SampleDto"
        await cacheService.RemoveByTagAsync("SampleDto");
        
        // Assert
        Assert.Null(await cacheService.GetAsync<string>("key1")); // Měl by být odstraněn (má SampleDto tag)
        Assert.Equal("value2", await cacheService.GetAsync<string>("key2")); // Měl by zůstat (nemá SampleDto tag)
        Assert.Null(await cacheService.GetAsync<string>("key3")); // Měl by být odstraněn (má SampleDto tag)
        Assert.Equal("value4", await cacheService.GetAsync<string>("key4")); // Měl by zůstat (nemá SampleDto tag)
    }
}
